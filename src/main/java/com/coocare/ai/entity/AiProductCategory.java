package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 产品分类实体类
 * 支持三级产品分类：产品类型 -> 产品型号 -> 产品系列
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_product_category")
@Schema(description = "产品分类管理")
public class AiProductCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分类ID
     */
    @TableId(value = "category_id", type = IdType.ASSIGN_ID)
    @Schema(description = "分类ID")
    private Long categoryId;

    /**
     * 分类名称
     */
    @TableField(value = "category_name")
    @Schema(description = "分类名称")
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    private String categoryName;

    /**
     * 分类编码
     */
    @TableField(value = "category_code")
    @Schema(description = "分类编码")
    @Size(max = 50, message = "分类编码长度不能超过50个字符")
    private String categoryCode;

    /**
     * 分类类型：1-产品类型，2-产品型号，3-产品系列
     */
    @TableField(value = "category_type")
    @Schema(description = "分类类型：1-产品类型，2-产品型号，3-产品系列")
    private Integer categoryType;

    /**
     * 父级分类ID，0表示顶级分类
     */
    @TableField(value = "parent_id")
    @Schema(description = "父级分类ID，0表示顶级分类")
    private Long parentId;

    /**
     * 层级路径，用/分隔，如：/1/2/3/
     */
    @TableField(value = "level_path")
    @Schema(description = "层级路径，用/分隔，如：/1/2/3/")
    @Size(max = 500, message = "层级路径长度不能超过500个字符")
    private String levelPath;

    /**
     * 排序
     */
    @TableField(value = "sort_order")
    @Schema(description = "排序")
    private Integer sortOrder;

    /**
     * 分类描述
     */
    @TableField(value = "description")
    @Schema(description = "分类描述")
    private String description;

    /**
     * 分类图标
     */
    @TableField(value = "icon")
    @Schema(description = "分类图标")
    @Size(max = 200, message = "分类图标长度不能超过200个字符")
    private String icon;

    /**
     * 状态：0-禁用，1-启用
     */
    @TableField(value = "status")
    @Schema(description = "状态：0-禁用，1-启用")
    private Integer status;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField(value = "del_flag")
    @TableLogic
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    private String delFlag;
}
